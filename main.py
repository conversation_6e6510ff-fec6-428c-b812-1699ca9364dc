#!/usr/bin/env python3
"""
Polygon MATIC Hyper-Short-Term Signal Bot
Main entry point for the AI-powered trading signal bot
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, Any

# Import Phase 2 data infrastructure
from src.data_collectors.polygon_rpc import polygon_rpc
from src.data_collectors.dex_collectors import quickswap_collector, uniswap_v3_collector
from src.data_collectors.coinglass_client import coinglass_client
from src.data_collectors.cache_manager import cache_manager
from src.data_collectors.price_aggregator import price_aggregator
from src.data_collectors.asset_selector import asset_selector
from src.data_collectors.data_validator import data_validator

from src.signal_engine.signal_generator import SignalGenerator
from src.paper_trading.portfolio_manager import PortfolioManager
from src.logging_system.trade_logger import TradeLogger
from config.settings import Config


class SignalBot:
    """Main Signal Bot orchestrator with Phase 2 data infrastructure"""

    def __init__(self):
        self.config = Config()
        self.signal_generator = SignalGenerator(self.config)
        self.portfolio_manager = PortfolioManager(self.config)
        self.trade_logger = TradeLogger(self.config)
        self.running = False

        # Phase 2 components
        self.data_systems_healthy = False

        # Set up logging
        logging.basicConfig(
            level=getattr(logging, self.config.LOG_LEVEL),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/signal_bot.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    async def scan_assets(self) -> Dict[str, Any]:
        """Scan and filter assets for trading opportunities"""
        try:
            return await self.signal_generator.scan_assets()
        except Exception as e:
            self.logger.error(f"Error scanning assets: {e}")
            return {}
    
    async def generate_signals(self, assets: Dict[str, Any]) -> list:
        """Generate trading signals from filtered assets"""
        try:
            return await self.signal_generator.generate_signals(assets)
        except Exception as e:
            self.logger.error(f"Error generating signals: {e}")
            return []
    
    async def execute_signals(self, signals: list):
        """Execute paper trades based on signals"""
        for signal in signals:
            try:
                trade_result = await self.portfolio_manager.execute_trade(signal)
                await self.trade_logger.log_trade(signal, trade_result)
            except Exception as e:
                self.logger.error(f"Error executing signal {signal}: {e}")
    
    async def run_cycle(self):
        """Run one complete bot cycle with Phase 2 data collection"""
        self.logger.info("Starting bot cycle")

        if not self.data_systems_healthy:
            self.logger.warning("Data systems unhealthy - running in degraded mode")
            # Fall back to placeholder behavior
            await self._run_placeholder_cycle()
            return

        try:
            # Phase 2: Collect real market data
            market_data = await self._collect_market_data()

            # Validate data quality
            data_quality = await self._validate_market_data(market_data)
            self.logger.info(f"Market data quality score: {data_quality.get('overall_quality', 0):.2f}")

            if data_quality.get('overall_quality', 0) < 0.3:
                self.logger.warning("Poor data quality - skipping cycle")
                return

            # Use Phase 2 data for asset scanning
            assets = market_data.get('top_assets', [])
            if not assets:
                self.logger.info("No assets found for analysis")
                return

            # Generate signals (placeholder - Phase 3/4)
            signals = await self.generate_signals({'assets': assets, 'market_data': market_data})
            if not signals:
                self.logger.info("No signals generated")
                return

            # Execute signals (placeholder - Phase 5)
            await self.execute_signals(signals)

            self.logger.info(f"Cycle completed - processed {len(signals)} signals with {len(assets)} assets")

        except Exception as e:
            self.logger.error(f"Error in enhanced cycle: {e}")
            # Fall back to placeholder behavior
            await self._run_placeholder_cycle()

    async def _run_placeholder_cycle(self):
        """Run placeholder cycle when data systems are unavailable"""
        # Original Phase 1 behavior
        assets = await self.scan_assets()
        if not assets:
            self.logger.info("No assets found for analysis (placeholder)")
            return

        signals = await self.generate_signals(assets)
        if not signals:
            self.logger.info("No signals generated (placeholder)")
            return

        await self.execute_signals(signals)
        self.logger.info(f"Placeholder cycle completed - processed {len(signals)} signals")

    async def _collect_market_data(self) -> Dict[str, Any]:
        """Collect comprehensive market data using Phase 2 systems"""
        try:
            # Get top trading assets
            top_assets = await asset_selector.get_top_assets()

            # Get current gas prices
            gas_prices = await polygon_rpc.get_gas_price()

            # Get market sentiment for MATIC
            sentiment_data = {}
            if hasattr(self.config, 'COINGLASS_API_KEY') and self.config.COINGLASS_API_KEY:
                sentiment_data = await coinglass_client.get_market_sentiment_summary("MATIC")

            # Get sample price data
            price_data = {}
            if top_assets and len(top_assets) > 0:
                sample_asset = top_assets[0]
                if sample_asset.get('trading_token'):
                    token_address = sample_asset['trading_token']['address']
                    price_info = await price_aggregator.get_token_price(token_address, "USDC")
                    if price_info:
                        price_data[token_address] = price_info

            market_data = {
                'timestamp': datetime.now().isoformat(),
                'top_assets': top_assets,
                'gas_prices': gas_prices,
                'sentiment': sentiment_data,
                'prices': price_data,
                'assets_count': len(top_assets)
            }

            self.logger.info(f"Market data collected: {len(top_assets)} assets, {len(price_data)} prices, sentiment={bool(sentiment_data)}")
            return market_data

        except Exception as e:
            self.logger.error(f"Market data collection failed: {e}")
            return {}

    async def _validate_market_data(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate collected market data quality"""
        try:
            quality_metrics = {
                'overall_quality': 1.0,
                'data_completeness': 0.0,
                'price_quality': 0.0,
                'sentiment_quality': 0.0
            }

            # Check data completeness
            required_fields = ['top_assets', 'gas_prices', 'timestamp']
            present_fields = sum(1 for field in required_fields if field in market_data and market_data[field])
            quality_metrics['data_completeness'] = present_fields / len(required_fields)

            # Validate price data if available
            prices = market_data.get('prices', {})
            if prices:
                price_validations = []
                for token_addr, price_info in prices.items():
                    validation = await data_validator.validate_price_data(price_info)
                    price_validations.append(validation['quality_score'])
                quality_metrics['price_quality'] = sum(price_validations) / len(price_validations)
            else:
                quality_metrics['price_quality'] = 0.5  # Neutral if no price data

            # Validate sentiment data if available
            sentiment = market_data.get('sentiment', {})
            if sentiment:
                sentiment_validation = await data_validator.validate_sentiment_data({'data': sentiment})
                quality_metrics['sentiment_quality'] = sentiment_validation['quality_score']
            else:
                quality_metrics['sentiment_quality'] = 0.5  # Neutral if no sentiment data

            # Calculate overall quality
            quality_metrics['overall_quality'] = (
                quality_metrics['data_completeness'] * 0.4 +
                quality_metrics['price_quality'] * 0.4 +
                quality_metrics['sentiment_quality'] * 0.2
            )

            return quality_metrics

        except Exception as e:
            self.logger.error(f"Data validation failed: {e}")
            return {'overall_quality': 0.0}
    
    async def start(self):
        """Start the bot main loop with Phase 2 data systems"""
        self.logger.info("Starting Polygon MATIC Signal Bot - Phase 2 Data Infrastructure")

        # Initialize and test data systems
        await self._initialize_data_systems()

        self.running = True

        while self.running:
            try:
                await self.run_cycle()

                # Sleep for configured interval (default 15 seconds)
                await asyncio.sleep(self.config.SCAN_INTERVAL)

            except KeyboardInterrupt:
                self.logger.info("Received shutdown signal")
                self.running = False
            except Exception as e:
                self.logger.error(f"Unexpected error in main loop: {e}")
                await asyncio.sleep(5)  # Brief pause before retry

        # Cleanup on exit
        await self._cleanup_data_systems()
    
    def stop(self):
        """Stop the bot"""
        self.logger.info("Stopping Signal Bot")
        self.running = False

    async def _initialize_data_systems(self):
        """Initialize and test all Phase 2 data collection systems"""
        self.logger.info("Initializing Phase 2 data systems...")

        try:
            # Test RPC connection
            rpc_health = await polygon_rpc.health_check()
            self.logger.info(f"RPC Health: {rpc_health.get('healthy', False)}")

            # Test cache system
            cache_health = await cache_manager.health_check()
            self.logger.info(f"Cache Health: Redis={cache_health.get('redis', {}).get('healthy', False)}, SQLite={cache_health.get('sqlite', {}).get('healthy', False)}")

            # Test data collectors with sample requests
            await self._test_data_collectors()

            self.data_systems_healthy = True
            self.logger.info("Phase 2 data systems initialized successfully")

        except Exception as e:
            self.logger.error(f"Data systems initialization failed: {e}")
            self.data_systems_healthy = False
            self.logger.warning("Continuing in degraded mode without real-time data")

    async def _test_data_collectors(self):
        """Test data collectors with sample requests"""
        try:
            # Test QuickSwap collector
            qs_pairs = await quickswap_collector.get_top_pairs(3)
            self.logger.info(f"QuickSwap test: {len(qs_pairs)} pairs fetched")

            # Test Uniswap V3 collector
            uni_pools = await uniswap_v3_collector.get_top_pools(3)
            self.logger.info(f"Uniswap V3 test: {len(uni_pools)} pools fetched")

            # Test Coinglass client (if API key available)
            if hasattr(self.config, 'COINGLASS_API_KEY') and self.config.COINGLASS_API_KEY:
                sentiment = await coinglass_client.get_market_sentiment_summary("MATIC")
                self.logger.info(f"Coinglass test: Sentiment data available = {bool(sentiment)}")
            else:
                self.logger.info("Coinglass test: Skipped (no API key)")

            # Test asset selector
            top_assets = await asset_selector.get_top_assets()
            self.logger.info(f"Asset selector test: {len(top_assets)} assets selected")

        except Exception as e:
            self.logger.warning(f"Data collectors test partial failure: {e}")

    async def _cleanup_data_systems(self):
        """Clean up Phase 2 data system resources"""
        self.logger.info("Cleaning up data systems...")

        try:
            await polygon_rpc.close()
            await quickswap_collector.close()
            await uniswap_v3_collector.close()
            await coinglass_client.close()
            await cache_manager.close()
            self.logger.info("Data systems cleanup completed")

        except Exception as e:
            self.logger.error(f"Data systems cleanup error: {e}")


async def main():
    """Main entry point"""
    bot = SignalBot()
    
    try:
        await bot.start()
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    finally:
        bot.stop()
        print("Signal Bot stopped")


if __name__ == "__main__":
    asyncio.run(main())
